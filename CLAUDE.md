# CLAUDE.md

## Project Overview

This is a full-stack application with a Vue 3 frontend and Django backend. The frontend is built with Vite and deployed to Cloudflare Workers, while the backend uses Django with SQLAlchemy and Django REST Framework.

## Development Commands

### Frontend (Vue 3 + Vite)
```bash
cd frontend
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Build and preview with <PERSON>rang<PERSON>
npm run deploy   # Build and deploy to Cloudflare
```

### Backend (Django)
```bash
cd backend
# Type checking commands:
ruff check       # Run Ruff linter
pyright          # Run Pyright type checker
mypy .           # Run MyPy type checker
```

## Technology Stack

**Frontend:**
- Vue 3
- Vite
- <PERSON>nia (state management)
- Vue Router
- Chart.js
- jsPDF
- Cloudflare Workers (Wrangler)

**Backend:**
- Django
- Django REST Framework
- SQLAlchemy
- Alembic (migrations)

**Development Tools:**
- Ruff (Python linter)
- Pyright (Python type checker)
- My<PERSON>y (Python type checker)
- Django stubs

## Directory Structure

- `frontend/` → Vue 3 application (symlinked to `/home/<USER>/deve/fesb`)
- `backend/` → Django application (symlinked to `/home/<USER>/projects/api_besb`)
- `backend-script.sh` → Deployment script (symlinked)

## Type Checking

The backend uses multiple type checking tools:

1. **Ruff**: Fast Python linter
2. **Pyright**: Microsoft's Python type checker
3. **MyPy**: Static type checker with Django plugin support

Run all type checks:
```bash
cd backend
ruff check
pyright
mypy .
```

## Deployment

**Frontend**: Deploys to Cloudflare Workers using Wrangler:
1. Build the application: `npm run build`
2. Deploy to Cloudflare: `npm run deploy`
3. Preview locally: `npm run preview`

**Backend**: Deployed using the backend deployment script with app name "besb":
```bash
./backend-script.sh besb <command>
```

Available commands:
- `init`: Initialize the project structure
- `start`: Start all services (PostgreSQL, Redis, Django, Uvicorn, Nginx, Cloudflare tunnel)
- `stop`: Stop and remove the pod
- `db`: Run database migrations
- `pg`: Start pgAdmin container
- `cek`: Check and restart services if needed
- `create_user`: Create a Django superuser
- `run_cmd`: Run arbitrary commands in the interactive container

Example usage:
```bash
./backend-script.sh besb start    # Start all services
./backend-script.sh besb db       # Run database migrations
./backend-script.sh besb stop     # Stop all services
```

## Development Workflow

1. Start frontend development: `cd frontend && npm run dev`
2. Run backend type checks: `cd backend && ruff check && pyright && mypy .`
3. Build and deploy: `cd frontend && npm run deploy`